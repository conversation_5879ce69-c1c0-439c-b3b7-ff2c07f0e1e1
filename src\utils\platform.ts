'use client';

// Platform detection utilities for cross-platform touch optimization

// Extend CSSStyleDeclaration to include WebKit properties
declare global {
  interface CSSStyleDeclaration {
    webkitTouchCallout?: string;
    webkitTapHighlightColor?: string;
    webkitOverflowScrolling?: string;
    webkitUserSelect?: string;
  }
}

export interface PlatformInfo {
  isIOS: boolean;
  isAndroid: boolean;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isTouchDevice: boolean;
  hasHover: boolean;
  supportsVibration: boolean;
  supportsShare: boolean;
  browserName: string;
  osVersion: string;
}

// Detect iOS devices
export const isIOS = (): boolean => {
  if (typeof window === 'undefined') return false;
  return /iPad|iPhone|iPod/.test(navigator.userAgent);
};

// Detect Android devices
export const isAndroid = (): boolean => {
  if (typeof window === 'undefined') return false;
  return /Android/.test(navigator.userAgent);
};

// Detect mobile devices
export const isMobile = (): boolean => {
  if (typeof window === 'undefined') return false;
  return /Mobi|Android/i.test(navigator.userAgent);
};

// Detect tablet devices
export const isTablet = (): boolean => {
  if (typeof window === 'undefined') return false;
  const userAgent = navigator.userAgent.toLowerCase();
  return /ipad|android(?!.*mobile)|tablet/.test(userAgent);
};

// Detect touch capability
export const isTouchDevice = (): boolean => {
  if (typeof window === 'undefined') return false;
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
};

// Detect hover capability
export const hasHover = (): boolean => {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(hover: hover)').matches;
};

// Check vibration support
export const supportsVibration = (): boolean => {
  if (typeof window === 'undefined') return false;
  return 'vibrate' in navigator;
};

// Check Web Share API support
export const supportsShare = (): boolean => {
  if (typeof window === 'undefined') return false;
  return 'share' in navigator;
};

// Get browser name
export const getBrowserName = (): string => {
  if (typeof window === 'undefined') return 'unknown';
  
  const userAgent = navigator.userAgent;
  
  if (userAgent.includes('Chrome')) return 'chrome';
  if (userAgent.includes('Firefox')) return 'firefox';
  if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) return 'safari';
  if (userAgent.includes('Edge')) return 'edge';
  if (userAgent.includes('Opera')) return 'opera';
  
  return 'unknown';
};

// Get OS version (simplified)
export const getOSVersion = (): string => {
  if (typeof window === 'undefined') return 'unknown';
  
  const userAgent = navigator.userAgent;
  
  // iOS version
  const iosMatch = userAgent.match(/OS (\d+)_(\d+)/);
  if (iosMatch) {
    return `iOS ${iosMatch[1]}.${iosMatch[2]}`;
  }
  
  // Android version
  const androidMatch = userAgent.match(/Android (\d+\.?\d*)/);
  if (androidMatch) {
    return `Android ${androidMatch[1]}`;
  }
  
  return 'unknown';
};

// Get comprehensive platform info
export const getPlatformInfo = (): PlatformInfo => {
  return {
    isIOS: isIOS(),
    isAndroid: isAndroid(),
    isMobile: isMobile(),
    isTablet: isTablet(),
    isDesktop: !isMobile() && !isTablet(),
    isTouchDevice: isTouchDevice(),
    hasHover: hasHover(),
    supportsVibration: supportsVibration(),
    supportsShare: supportsShare(),
    browserName: getBrowserName(),
    osVersion: getOSVersion(),
  };
};

// iOS specific optimizations
export const applyIOSOptimizations = () => {
  if (!isIOS()) return;
  
  // Disable iOS bounce scrolling on body
  document.body.style.overscrollBehavior = 'none';
  
  // Fix iOS viewport height issues
  const setVH = () => {
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
  };
  
  setVH();
  window.addEventListener('resize', setVH);
  window.addEventListener('orientationchange', setVH);
  
  // Disable iOS text size adjustment
  document.documentElement.style.webkitTextSizeAdjust = '100%';
  
  // Disable iOS callouts and selection
  document.documentElement.style.webkitTouchCallout = 'none';
  document.documentElement.style.webkitUserSelect = 'none';
};

// Android specific optimizations
export const applyAndroidOptimizations = () => {
  if (!isAndroid()) return;
  
  // Disable Android tap highlight
  document.documentElement.style.webkitTapHighlightColor = 'transparent';
  
  // Optimize Android scrolling
  document.documentElement.style.overscrollBehavior = 'contain';
  
  // Handle Android back button (if in PWA)
  if ('serviceWorker' in navigator) {
    window.addEventListener('beforeunload', (e) => {
      // Custom back button handling can be added here
    });
  }
};

// Apply all platform optimizations
export const applyPlatformOptimizations = () => {
  applyIOSOptimizations();
  applyAndroidOptimizations();
  
  // Universal touch optimizations
  if (isTouchDevice()) {
    // Improve touch scrolling
    document.documentElement.style.webkitOverflowScrolling = 'touch';
    
    // Prevent zoom on input focus (iOS)
    if (isIOS()) {
      const inputs = document.querySelectorAll('input, select, textarea');
      inputs.forEach((input) => {
        if (input instanceof HTMLElement) {
          input.style.fontSize = '16px'; // Prevents zoom on iOS
        }
      });
    }
  }
};

// Haptic feedback patterns for different platforms
export const platformHaptics = {
  light: () => {
    if (supportsVibration()) {
      if (isIOS()) {
        navigator.vibrate(10); // Light tap for iOS
      } else if (isAndroid()) {
        navigator.vibrate(25); // Slightly longer for Android
      }
    }
  },
  
  medium: () => {
    if (supportsVibration()) {
      if (isIOS()) {
        navigator.vibrate(20);
      } else if (isAndroid()) {
        navigator.vibrate(50);
      }
    }
  },
  
  heavy: () => {
    if (supportsVibration()) {
      if (isIOS()) {
        navigator.vibrate([30, 10, 30]);
      } else if (isAndroid()) {
        navigator.vibrate([100, 50, 100]);
      }
    }
  },
  
  success: () => {
    if (supportsVibration()) {
      if (isIOS()) {
        navigator.vibrate([50, 25, 50]);
      } else if (isAndroid()) {
        navigator.vibrate([100, 50, 100, 50, 100]);
      }
    }
  },
  
  error: () => {
    if (supportsVibration()) {
      if (isIOS()) {
        navigator.vibrate([100, 50, 100, 50, 100]);
      } else if (isAndroid()) {
        navigator.vibrate([200, 100, 200, 100, 200]);
      }
    }
  },
};

// Platform-specific CSS classes
export const getPlatformClasses = (): string[] => {
  const classes: string[] = [];
  
  if (isIOS()) classes.push('platform-ios');
  if (isAndroid()) classes.push('platform-android');
  if (isMobile()) classes.push('platform-mobile');
  if (isTablet()) classes.push('platform-tablet');
  if (!isMobile() && !isTablet()) classes.push('platform-desktop');
  if (isTouchDevice()) classes.push('platform-touch');
  if (hasHover()) classes.push('platform-hover');
  
  return classes;
};

// Initialize platform optimizations on load
export const initializePlatformOptimizations = () => {
  if (typeof window === 'undefined') return;
  
  // Apply platform classes to document
  const classes = getPlatformClasses();
  document.documentElement.classList.add(...classes);
  
  // Apply platform-specific optimizations
  applyPlatformOptimizations();
  
  // Log platform info for debugging
  if (process.env.NODE_ENV === 'development') {
    console.log('Platform Info:', getPlatformInfo());
  }
};
